<!--部门角色选择组件-->
<template>
  <div class="j-dept-role-selector">
    <div class="selector-input" @click="handleOpen" :class="{ disabled: disabled }">
      <div v-if="selectedData.departName" class="selected-content">
        <div class="dept-info">
          <Icon icon="ant-design:apartment-outlined" />
          <span class="dept-name">{{ selectedData.departName }}</span>
        </div>
        <div v-if="selectedData.roles && selectedData.roles.length > 0" class="roles-info">
          <div class="role-count">已选择 {{ selectedData.roles.length }} 个角色</div>
          <div class="role-list">
            <a-tag v-for="(role, index) in selectedData.roles.slice(0, 3)" :key="role.id" color="blue">
              {{ role.roleName }}
            </a-tag>
            <a-tag v-if="selectedData.roles.length > 3" color="orange">
              +{{ selectedData.roles.length - 3 }}...
            </a-tag>
          </div>
        </div>
      </div>
      <div v-else class="placeholder">
        <Icon icon="ant-design:plus-outlined" />
        <span>请选择部门和角色</span>
      </div>
      <Icon icon="ant-design:down-outlined" class="arrow-icon" />
    </div>
    
    <!-- 弹窗 -->
    <DeptRoleSelectorModal 
      @register="registerModal" 
      @confirm="handleConfirm"
      :value="selectedData"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { useModal } from '/@/components/Modal';
import { Icon } from '/@/components/Icon';
import DeptRoleSelectorModal from './modal/DeptRoleSelectorModal.vue';

defineOptions({ name: 'JDeptRoleSelector' });

interface RoleItem {
  id: string;
  roleName: string;
  roleCode: string;
  description?: string;
  sortOrder?: number;
}

interface SelectedData {
  departId?: string;
  departName?: string;
  sysOrgCode?: string;
  roles?: RoleItem[];
}

interface Props {
  value?: any;
  disabled?: boolean;
  placeholder?: string;
}

interface Emits {
  (e: 'update:value', value: any): void;
  (e: 'change', value: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  placeholder: '请选择部门和角色'
});

const emit = defineEmits<Emits>();

// 注册弹窗
const [registerModal, { openModal }] = useModal();

// 选中的数据
const selectedData = ref<SelectedData>({});

// 监听外部传入的值
watch(() => props.value, (newValue) => {
  if (newValue) {
    try {
      if (typeof newValue === 'string') {
        selectedData.value = JSON.parse(newValue);
      } else {
        selectedData.value = newValue;
      }
    } catch (error) {
      console.warn('JDeptRoleSelector: Invalid value format', error);
      selectedData.value = {};
    }
  } else {
    selectedData.value = {};
  }
}, { immediate: true });

// 打开弹窗
const handleOpen = () => {
  if (props.disabled) return;
  openModal(true, selectedData.value);
};

// 确认选择
const handleConfirm = (data: SelectedData) => {
  selectedData.value = data;
  
  // 构造返回值
  const result = {
    sysOrgCode: data.sysOrgCode,
    departId: data.departId,
    departName: data.departName,
    roleIds: data.roles?.map(role => role.id) || [],
    roles: data.roles || []
  };
  
  emit('update:value', result);
  emit('change', result);
};
</script>

<style lang="less" scoped>
.j-dept-role-selector {
  .selector-input {
    min-height: 32px;
    padding: 4px 11px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    background: #fff;
    position: relative;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &.disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
      
      &:hover {
        border-color: #d9d9d9;
      }
    }
    
    .selected-content {
      .dept-info {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        
        .anticon {
          margin-right: 6px;
          color: #1890ff;
        }
        
        .dept-name {
          font-weight: 500;
          color: #262626;
        }
      }
      
      .roles-info {
        .role-count {
          font-size: 12px;
          color: #8c8c8c;
          margin-bottom: 4px;
        }
        
        .role-list {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }
      }
    }
    
    .placeholder {
      display: flex;
      align-items: center;
      color: #bfbfbf;
      
      .anticon {
        margin-right: 6px;
      }
    }
    
    .arrow-icon {
      position: absolute;
      right: 11px;
      top: 50%;
      transform: translateY(-50%);
      color: #bfbfbf;
      transition: transform 0.3s;
    }
  }
}
</style>
