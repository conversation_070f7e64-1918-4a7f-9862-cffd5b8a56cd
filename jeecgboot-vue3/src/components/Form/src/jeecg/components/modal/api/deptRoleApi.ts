import { defHttp } from '/@/utils/http/axios';

/**
 * 获取部门角色列表
 * @param params 
 */
export const getDeptRoleList = (params: { departId: string; userId?: string }) => {
  return defHttp.get({
    url: '/sys/sysDepartRole/getDeptRoleList',
    params
  });
};

/**
 * 根据部门组织编码获取部门角色列表
 * @param params { sysOrgCode: string } 部门组织编码
 */
export const getDeptRoleListBySysOrgCode = (params: { sysOrgCode: string }) => {
  console.log('调用部门角色API（根据sysOrgCode），参数:', params);
  return defHttp.get({
    url: '/emsdepartrole/emsDepartRole/queryBySysOrgCode',
    params: {
      sysOrgCode: params.sysOrgCode
    }
  }).then(res => {
    console.log('部门角色API返回:', res);
    const roleList = res || [];
    return roleList.map(item => ({
      id: item.id,
      roleName: item.roleName,
      roleCode: item.roleCode,
      description: item.description
    }));
  }).catch(error => {
    console.error('部门角色API调用失败:', error);
    return [];
  });
};

/**
 * 根据部门ID获取部门角色列表
 * @param params { departId: string } 部门ID
 */
export const getDeptRoleListByDepartId = (params: { departId: string }) => {
  console.log('调用部门角色API（根据departId），参数:', params);
  return defHttp.get({
    url: '/emsdepartrole/emsDepartRole/queryByDepartId',
    params: {
      departId: params.departId
    }
  }).then(res => {
    console.log('部门角色API返回:', res);
    const roleList = res || [];
    return roleList.map(item => ({
      id: item.id,
      roleName: item.roleName,
      roleCode: item.roleCode,
      description: item.description
    }));
  }).catch(error => {
    console.error('部门角色API调用失败:', error);
    return [];
  });
};
