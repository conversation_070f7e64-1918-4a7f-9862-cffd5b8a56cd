<template>
  <div class="dept-role-selector-demo">
    <PageWrapper title="部门角色选择组件示例">
      <div class="demo-container">
        <a-card title="基础用法" class="demo-card">
          <div class="demo-item">
            <div class="demo-label">部门角色选择器：</div>
            <JDeptRoleSelector 
              v-model:value="formData.deptRole" 
              @change="handleChange"
              style="width: 400px;"
            />
          </div>
          
          <div class="demo-item">
            <div class="demo-label">禁用状态：</div>
            <JDeptRoleSelector 
              v-model:value="formData.deptRole" 
              :disabled="true"
              style="width: 400px;"
            />
          </div>
          
          <a-divider />
          
          <div class="result-section">
            <h4>选择结果：</h4>
            <pre>{{ JSON.stringify(formData.deptRole, null, 2) }}</pre>
          </div>
          
          <div class="action-section">
            <a-space>
              <a-button @click="clearSelection">清空选择</a-button>
              <a-button type="primary" @click="setDefaultValue">设置默认值</a-button>
              <a-button @click="getSelectedData">获取选择数据</a-button>
            </a-space>
          </div>
        </a-card>
        
        <a-card title="表单集成示例" class="demo-card">
          <BasicForm @register="registerForm" />
          <div class="form-actions">
            <a-space>
              <a-button @click="resetForm">重置表单</a-button>
              <a-button type="primary" @click="submitForm">提交表单</a-button>
            </a-space>
          </div>
        </a-card>
      </div>
    </PageWrapper>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { PageWrapper } from '/@/components/Page';
import { BasicForm, useForm, FormSchema } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import JDeptRoleSelector from '/@/components/Form/src/jeecg/components/JDeptRoleSelector.vue';

const { createMessage } = useMessage();

// 基础示例数据
const formData = reactive({
  deptRole: null
});

// 表单配置
const formSchemas: FormSchema[] = [
  {
    field: 'title',
    component: 'Input',
    label: '标题',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptRole',
    component: 'JDeptRoleSelector',
    label: '部门角色',
    required: true,
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请选择部门和角色'
    }
  },
  {
    field: 'description',
    component: 'InputTextArea',
    label: '描述',
    colProps: { span: 24 },
    componentProps: {
      rows: 4
    }
  }
];

// 注册表单
const [registerForm, { getFieldsValue, setFieldsValue, resetFields, validate }] = useForm({
  labelWidth: 120,
  schemas: formSchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 }
});

// 处理选择变化
const handleChange = (value: any) => {
  console.log('部门角色选择变化:', value);
  createMessage.info('选择已更新，请查看控制台输出');
};

// 清空选择
const clearSelection = () => {
  formData.deptRole = null;
  createMessage.success('已清空选择');
};

// 设置默认值
const setDefaultValue = () => {
  const defaultValue = {
    sysOrgCode: 'A01',
    departId: '1',
    departName: '总公司',
    roleIds: ['role1', 'role2'],
    roles: [
      {
        id: 'role1',
        roleName: '部门经理',
        roleCode: 'DEPT_MANAGER',
        sortOrder: 1
      },
      {
        id: 'role2',
        roleName: '普通员工',
        roleCode: 'EMPLOYEE',
        sortOrder: 2
      }
    ]
  };
  formData.deptRole = defaultValue;
  createMessage.success('已设置默认值');
};

// 获取选择数据
const getSelectedData = () => {
  if (formData.deptRole) {
    createMessage.success('数据已输出到控制台');
    console.log('当前选择的数据:', formData.deptRole);
  } else {
    createMessage.warning('请先选择部门和角色');
  }
};

// 重置表单
const resetForm = () => {
  resetFields();
  createMessage.success('表单已重置');
};

// 提交表单
const submitForm = async () => {
  try {
    const values = await validate();
    console.log('表单数据:', values);
    createMessage.success('表单提交成功，请查看控制台输出');
  } catch (error) {
    createMessage.error('表单验证失败');
  }
};
</script>

<style lang="less" scoped>
.dept-role-selector-demo {
  .demo-container {
    .demo-card {
      margin-bottom: 24px;
      
      .demo-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
        
        .demo-label {
          width: 150px;
          flex-shrink: 0;
          font-weight: 500;
          line-height: 32px;
        }
      }
      
      .result-section {
        margin-top: 24px;
        
        h4 {
          margin-bottom: 12px;
        }
        
        pre {
          background: #f5f5f5;
          padding: 12px;
          border-radius: 4px;
          font-size: 12px;
          max-height: 300px;
          overflow-y: auto;
        }
      }
      
      .action-section {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
      }
    }
    
    .form-actions {
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }
  }
}
</style>
