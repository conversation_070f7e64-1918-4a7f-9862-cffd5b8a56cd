<template>
  <div class="dept-tree-debug">
    <a-card title="部门树和角色API调试">
      <div class="debug-section">
        <h3>API测试</h3>
        <a-space>
          <a-button @click="testDeptTreeAPI" type="primary">测试部门树API</a-button>
          <a-button @click="testDeptRoleBySysOrgCode">测试角色API(sysOrgCode)</a-button>
          <a-button @click="testDeptRoleByDepartId">测试角色API(departId)</a-button>
        </a-space>

        <div class="input-section">
          <a-space>
            <a-input v-model:value="testSysOrgCode" placeholder="输入sysOrgCode" style="width: 200px;" />
            <a-input v-model:value="testDepartId" placeholder="输入departId" style="width: 200px;" />
          </a-space>
        </div>

        <div class="result-section">
          <h4>API返回数据：</h4>
          <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>
      </div>

      <a-divider />

      <div class="tree-section">
        <h3>部门树组件测试</h3>
        <div style="height: 400px; border: 1px solid #f0f0f0; padding: 16px;">
          <BasicTree
            :treeData="treeData"
            :fieldNames="{ key: 'key', title: 'title', children: 'children' }"
            @select="onTreeSelect"
            :selectedKeys="selectedKeys"
            :checkable="false"
            :showLine="true"
            :showIcon="false"
          />
        </div>

        <div class="selection-info">
          <h4>选择信息：</h4>
          <p>选中的Key: {{ selectedKeys }}</p>
          <p>选中的节点信息:</p>
          <pre>{{ JSON.stringify(selectedNodeInfo, null, 2) }}</pre>

          <div v-if="selectedNodeInfo">
            <a-space>
              <a-button @click="testSelectedDeptRole" type="primary">测试选中部门的角色</a-button>
            </a-space>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BasicTree } from '/@/components/Tree';
import { queryDepartTreeSync } from '/@/api/common/api';
import { getDeptRoleListBySysOrgCode, getDeptRoleListByDepartId } from '/@/components/Form/src/jeecg/components/modal/api/deptRoleApi';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();

const apiResult = ref(null);
const treeData = ref([]);
const selectedKeys = ref([]);
const selectedNodeInfo = ref(null);
const testSysOrgCode = ref('A01');
const testDepartId = ref('1');

// 测试部门树API
const testDeptTreeAPI = async () => {
  try {
    createMessage.loading('正在加载部门树数据...');
    const result = await queryDepartTreeSync();
    apiResult.value = result;
    treeData.value = result || [];
    createMessage.success('部门树数据加载成功');
    console.log('部门树API返回:', result);
  } catch (error) {
    console.error('部门树API调用失败:', error);
    createMessage.error('部门树数据加载失败: ' + error.message);
    apiResult.value = { error: error.message };
  }
};

// 测试部门角色API(通过sysOrgCode)
const testDeptRoleBySysOrgCode = async () => {
  try {
    createMessage.loading('正在加载部门角色数据(sysOrgCode)...');

    const sysOrgCode = testSysOrgCode.value;
    console.log('使用sysOrgCode查询角色:', sysOrgCode);

    const result = await getDeptRoleListBySysOrgCode({ sysOrgCode });
    apiResult.value = {
      使用的sysOrgCode: sysOrgCode,
      查询结果: result
    };
    createMessage.success('部门角色数据加载成功');
    console.log('部门角色API(sysOrgCode)返回:', result);
  } catch (error) {
    console.error('部门角色API调用失败:', error);
    createMessage.error('部门角色数据加载失败: ' + error.message);
    apiResult.value = { error: error.message };
  }
};

// 测试部门角色API(通过departId)
const testDeptRoleByDepartId = async () => {
  try {
    createMessage.loading('正在加载部门角色数据(departId)...');

    const departId = testDepartId.value;
    console.log('使用departId查询角色:', departId);

    const result = await getDeptRoleListByDepartId({ departId });
    apiResult.value = {
      使用的departId: departId,
      查询结果: result
    };
    createMessage.success('部门角色数据加载成功');
    console.log('部门角色API(departId)返回:', result);
  } catch (error) {
    console.error('部门角色API调用失败:', error);
    createMessage.error('部门角色数据加载失败: ' + error.message);
    apiResult.value = { error: error.message };
  }
};

// 树选择事件
const onTreeSelect = (selectedKeysValue: string[], info: any) => {
  selectedKeys.value = selectedKeysValue;
  selectedNodeInfo.value = info.node;
  console.log('树选择事件:', {
    selectedKeys: selectedKeysValue,
    nodeInfo: info.node
  });
  createMessage.info('已选择部门: ' + (info.node?.title || info.node?.departName || '未知'));
};

// 测试选中部门的角色
const testSelectedDeptRole = async () => {
  if (!selectedNodeInfo.value) {
    createMessage.warning('请先选择一个部门');
    return;
  }

  const sysOrgCode = selectedNodeInfo.value.orgCode || selectedNodeInfo.value.key;
  const departId = selectedNodeInfo.value.id || selectedNodeInfo.value.key;

  if (!sysOrgCode && !departId) {
    createMessage.error('选中的部门没有有效的orgCode或ID');
    return;
  }

  try {
    createMessage.loading('正在加载选中部门的角色...');

    let result = [];
    let queryMethod = '';

    // 优先使用sysOrgCode查询
    if (sysOrgCode) {
      console.log('使用sysOrgCode查询角色:', sysOrgCode);
      result = await getDeptRoleListBySysOrgCode({ sysOrgCode });
      queryMethod = 'sysOrgCode';
    } else if (departId) {
      console.log('使用departId查询角色:', departId);
      result = await getDeptRoleListByDepartId({ departId });
      queryMethod = 'departId';
    }

    apiResult.value = {
      sysOrgCode,
      departId,
      queryMethod,
      roleCount: result.length,
      roles: result,
      nodeInfo: {
        id: selectedNodeInfo.value.id,
        key: selectedNodeInfo.value.key,
        title: selectedNodeInfo.value.title,
        departName: selectedNodeInfo.value.departName,
        orgCode: selectedNodeInfo.value.orgCode
      }
    };

    createMessage.success(`通过${queryMethod}查询到 ${result.length} 个角色`);
    console.log('选中部门的角色查询结果:', {
      queryMethod,
      sysOrgCode,
      departId,
      result
    });
  } catch (error) {
    console.error('查询选中部门角色失败:', error);
    createMessage.error('查询失败: ' + error.message);
    apiResult.value = { error: error.message };
  }
};

// 测试自定义departId
const testCustomDepartId = async () => {
  if (!testDepartId.value) {
    createMessage.warning('请输入departId');
    return;
  }
  await testDeptRoleByDepartId();
};

// 页面加载时自动测试
testDeptTreeAPI();
</script>

<style lang="less" scoped>
.dept-tree-debug {
  padding: 20px;

  .debug-section {
    margin-bottom: 24px;

    .input-section {
      margin: 16px 0;
    }
  }

  .result-section {
    margin-top: 16px;

    pre {
      background: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
    }
  }

  .tree-section {
    .selection-info {
      margin-top: 16px;
      padding: 16px;
      background: #fafafa;
      border-radius: 4px;

      pre {
        background: #fff;
        padding: 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }
}
</style>
