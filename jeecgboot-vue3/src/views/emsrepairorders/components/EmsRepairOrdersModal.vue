<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
      <BasicForm @register="registerForm" name="EmsRepairOrdersForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref, reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {formSchema} from '../EmsRepairOrders.data';
    import {saveOrUpdate, checkActiveTemplate} from '../EmsRepairOrders.api';
    import { useMessage } from '/@/hooks/web/useMessage';
    import { getDateByPicker } from '/@/utils';
    const { createMessage } = useMessage();
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const isDetail = ref(false);
    //表单配置
    const [registerForm, { setProps,resetFields, setFieldsValue, validate, scrollToField }] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 24}
    });
    //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await resetFields();
        setModalProps({confirmLoading: false,showCancelBtn:!!data?.showFooter,showOkBtn:!!data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        isDetail.value = !!data?.showFooter;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
        } else {
            // 新增时设置默认值并获取当前审批模板
            try {
                const templateResult = await checkActiveTemplate();
                console.log('获取到的审批模板:', templateResult);

                await setFieldsValue({
                    currentStatus: '1', // 默认状态为审核中
                    approvalUserConfig: [], // 显式设置审批人员配置为空数组
                });

                // 只显示审批人员配置字段
                if (templateResult.selectedRoles && templateResult.selectedRoles.length > 0) {
                    setProps({
                        schemas: formSchema.map(schema => {
                            if (schema.field === 'approvalUserConfig') {
                                return {
                                    ...schema,
                                    show: true,
                                    componentProps: {
                                        ...schema.componentProps,
                                        approvalSteps: templateResult.selectedRoles || [],
                                    }
                                };
                            }
                            return schema;
                        })
                    });
                } else {
                    createMessage.warning('当前部门没有激活的审批模板，请联系管理员配置');
                }

            } catch (error) {
                console.error('获取审批模板失败:', error);
                createMessage.error('获取审批模板失败: ' + (error.message || '未知错误'));
                await setFieldsValue({
                    currentStatus: '1',
                });
            }
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //日期个性化选择
    const fieldPickers = reactive({
    });
    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
    //表单提交事件
    async function handleSubmit(v) {
        try {
            let values = await validate();
            // 预处理日期数据
            changeDateValue(values);

            // 打印表单数据用于调试
            console.log('表单提交数据:', values);
            console.log('审批人员配置:', values.approvalUserConfig);

            // 如果是新增模式，验证审批人员配置
            if (!isUpdate.value) {
                console.log('验证审批人员配置:', values.approvalUserConfig);
                console.log('approvalUserConfig 类型:', typeof values.approvalUserConfig);
                console.log('approvalUserConfig 是否为数组:', Array.isArray(values.approvalUserConfig));

                if (!values.approvalUserConfig || !Array.isArray(values.approvalUserConfig) || values.approvalUserConfig.length === 0) {
                    createMessage.error('请配置审批人员！');
                    return Promise.reject('审批人员配置为空');
                }

                // 验证每个审批步骤是否都选择了用户
                const hasEmptyStep = values.approvalUserConfig.some(step =>
                    !step.selectedUsers || step.selectedUsers.length === 0
                );

                if (hasEmptyStep) {
                    createMessage.error('请为每个审批步骤选择审批人员！');
                    return Promise.reject('存在未配置审批人员的步骤');
                }
            }

            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } catch (error) {
           console.error('表单提交错误:', error);

           // 处理表单验证错误
           if (error && error.errorFields) {
             const firstField = error.errorFields[0];
             if (firstField) {
               scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
             }
             return Promise.reject(error.errorFields);
           }

           // 处理其他错误
           if (typeof error === 'string') {
             return Promise.reject(error);
           }

           createMessage.error('提交失败: ' + (error.message || '未知错误'));
           return Promise.reject(error);
        } finally {
            setModalProps({confirmLoading: false});
        }
    }

    /**
     * 处理日期值
     * @param formData 表单数据
     */
    const changeDateValue = (formData) => {
        if (formData && fieldPickers) {
            for (let key in fieldPickers) {
                if (formData[key]) {
                    formData[key] = getDateByPicker(formData[key], fieldPickers[key]);
                }
            }
        }
    };

</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
