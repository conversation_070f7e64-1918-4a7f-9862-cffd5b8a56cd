<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="1000px">
      <BasicForm @register="registerForm" ref="formRef"/>
  <!-- 子表单区域 -->
      <a-tabs v-model:activeKey="activeKey" @change="handleChangeTabs">
<#list subTables as sub><#rt/>
  <#assign refKey = sub.entityName?uncap_first/>
        <a-tab-pane tab="${sub.ftlDescription}" key="${refKey}" :forceRender="true">
            <JVxeTable
              keep-source
              resizable
              ref="${refKey}"
              :loading="${sub.entityName?uncap_first}Table.loading"
              :columns="${sub.entityName?uncap_first}Table.columns"
              :dataSource="${sub.entityName?uncap_first}Table.dataSource"
              :height="340"
              :rowNumber="true"
              :rowSelection="true"
              :toolbar="true"
              />
        </a-tab-pane>
</#list>
      </a-tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref,reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts'
    import {formSchema<#list subTables as sub>,${sub.entityName?uncap_first}Columns</#list>} from '../${entityName}.data';
    import {saveOrUpdate<#list subTables as sub>,${sub.entityName?uncap_first}List</#list>} from '../${entityName}.api';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const refKeys = ref([<#list subTables as sub>'${sub.entityName?uncap_first}', </#list>]);
    <#assign hasOne2Many = false>
    const activeKey = ref('${subTables[0].entityName?uncap_first}');
<#list subTables as sub>
    const ${sub.entityName?uncap_first} = ref();
</#list>
    const tableRefs = {<#list subTables as sub>${sub.entityName?uncap_first},</#list>};
   <#list subTables as sub>
    const ${sub.entityName?uncap_first}Table = reactive({
          loading: false,
          dataSource: [],
          columns:${sub.entityName?uncap_first}Columns
    })
   </#list>
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
    });
     //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await reset();
        setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
            <#list subTables as sub><#rt/>
             requestSubTableData(${sub.entityName?uncap_first}List, {id:data?.record?.id}, ${sub.entityName?uncap_first}Table)
            </#list>
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //方法配置
    const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys);

    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));

    async function reset(){
      await resetFields();
      activeKey.value = '${subTables[0].entityName?uncap_first}';
      <#list subTables as sub>
      ${sub.entityName?uncap_first}Table.dataSource = [];
      </#list>
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue)
         return {
           ...main, // 展开
 <#assign subManyIndex = 0>
 <#list subTables as sub><#rt/>
           ${sub.entityName?uncap_first}List: allValues.tablesValue[${subManyIndex}].tableData,
           <#assign subManyIndex = subManyIndex+1>
 </#list>
         }
       }

    //表单提交事件
    async function requestAddOrEdit(values) {
        try {
            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } finally {
            setModalProps({confirmLoading: false});
        }
    }
</script>

<style lang="less" scoped>

</style>