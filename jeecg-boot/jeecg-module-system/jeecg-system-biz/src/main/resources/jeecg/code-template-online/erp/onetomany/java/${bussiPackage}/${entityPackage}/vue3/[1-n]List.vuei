<#list subTables as sub>
#segment#${sub.entityName}List.vue
<template>
 <div>
     <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" :searchInfo="searchInfo">
      <!--插槽:table标题-->
       <template #tableTitle>
           <a-button type="primary" @click="handleCreate" preIcon="ant-design:plus-outlined" v-if="mainId!=''"> 新增</a-button>
           <a-button  type="primary" preIcon="ant-design:export-outlined" @click="onExportXls" v-if="mainId!=''"> 导出</a-button>
           <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls" v-if="mainId!=''">导入</j-upload-button>
           <a-dropdown v-if="selectedRowKeys.length > 0">
               <template #overlay>
                 <a-menu>
                   <a-menu-item key="1" @click="batchHandleDelete">
                     <Icon icon="ant-design:delete-outlined"></Icon>
                     删除
                   </a-menu-item>
                 </a-menu>
               </template>
               <a-button>批量操作
                 <Icon icon="mdi:chevron-down"></Icon>
               </a-button>
         </a-dropdown>
       </template>
        <!--操作栏-->
       <template #action="{ record }">
         <TableAction :actions="getTableAction(record)"/>
       </template>
       <!--字段回显插槽-->
       <template v-slot:bodyCell="{ column, record, index, text }">
       <#list sub.originalColumns as po>
         <#if po.classType=='umeditor' || po.classType=='pca' || po.classType=='file'>
         <template v-if="column.dataIndex==='${po.fieldName}'">
         <#if po.classType=='umeditor'>
           <!--富文本件字段回显插槽-->
           <div v-html="text"></div>
         </#if>
         <#if po.classType=='file'>
           <!--文件字段回显插槽-->
           <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
           <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small" @click="downloadFile(text)">下载</a-button>
         </#if>
         </template>
        </#if>
       </#list>
       </template>
     </BasicTable>

      <${sub.entityName}Modal @register="registerModal" @success="handleSuccess"/>
   </div>
</template>

<script lang="ts" setup>
  import {ref, computed, unref,inject,watch} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage'
  import {useModal} from '/@/components/Modal';
  import ${sub.entityName}Modal from './components/${sub.entityName}Modal.vue'
  import {${sub.entityName?uncap_first}Columns} from './${entityName}.data';
  import {${sub.entityName?uncap_first}List, ${sub.entityName?uncap_first}Delete, ${sub.entityName?uncap_first}DeleteBatch, ${sub.entityName?uncap_first}ExportXlsUrl, ${sub.entityName?uncap_first}ImportUrl } from './${entityName}.api';
  import {isEmpty} from "/@/utils/is";
  import {useMessage} from '/@/hooks/web/useMessage';
  import {downloadFile} from '/@/utils/common/renderUtils';

    //接收主表id
    const mainId = inject('mainId') || '';
    //提示弹窗
    const $message = useMessage()
    //弹窗model
    const [registerModal, {openModal}] = useModal();
    const searchInfo = {};
    // 列表页面公共参数、方法
    const {prefixCls, tableContext, onImportXls, onExportXls} = useListPage({
        tableProps: {
            api: ${sub.entityName?uncap_first}List,
            columns: ${sub.entityName?uncap_first}Columns,
            canResize: true,
            useSearchForm: false,
            actionColumn: {
                width: 180,
                fixed:'right'
            },
            pagination:{
                current: 1,
                pageSize: 5,
                pageSizeOptions: ['5', '10', '20'],
            }
        },
        exportConfig: {
            name: '${sub.ftlDescription}',
            url: ${sub.entityName?uncap_first}ExportXlsUrl,
            params: {
                <#list sub.foreignKeys as key>
                '${key?uncap_first}': mainId
                </#list>
            }
        },
        importConfig: {
            url: ()=>{
                return ${sub.entityName?uncap_first}ImportUrl + '/' + unref(mainId)
            }
        }
    });

    //注册table数据
    const [registerTable, {reload<#if sub.foreignRelationType =='1'>,getDataSource</#if>}, {rowSelection, selectedRowKeys}] = tableContext;

    watch(mainId, () => {
     <#list sub.foreignKeys as key>
         searchInfo['${key?uncap_first}'] = unref(mainId);
     </#list>
         reload();
      }
    );

    /**
     * 新增事件
     */
    function handleCreate() {
        if (isEmpty(unref(mainId))) {
            $message.createMessage.warning('请选择一个主表信息')
            return;
        }
        <#if sub.foreignRelationType =='1'>
        let dataSource = getDataSource();
        if(dataSource.length  >=  1){
            $message.createMessage.warning('一对一子表只能添加一条数据')
            return;
        }
        </#if>
        openModal(true, {
            isUpdate: false,
            showFooter: true,
        });
    }

    /**
     * 编辑事件
     */
    async function handleEdit(record: Recordable) {
        openModal(true, {
            record,
            isUpdate: true,
            showFooter: true,
        });
    }

    /**
     * 删除事件
     */
    async function handleDelete(record) {
        await ${sub.entityName?uncap_first}Delete({id: record.id}, handleSuccess);
    }

    /**
     * 批量删除事件
     */
    async function batchHandleDelete() {
        await ${sub.entityName?uncap_first}DeleteBatch({ids: selectedRowKeys.value}, handleSuccess)
    }

    /**
     * 成功回调
     */
    function handleSuccess() {
        (selectedRowKeys.value = []) && reload();
    }

    /**
     * 操作栏
     */
    function getTableAction(record) {
        return [
            {
                label: '编辑',
                onClick: handleEdit.bind(null, record),
            }, {
                label: '删除',
                popConfirm: {
                    title: '是否确认删除',
                    confirm: handleDelete.bind(null, record),
                    placement: 'topLeft',
                },
            }
        ]
    }
</script>
</#list>