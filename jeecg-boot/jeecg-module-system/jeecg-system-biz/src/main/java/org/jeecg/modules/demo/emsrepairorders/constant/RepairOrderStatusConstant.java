package org.jeecg.modules.demo.emsrepairorders.constant;

/**
 * @Description: 维修工单状态常量
 * @Author: jeecg-boot
 * @Date: 2025-08-04
 * @Version: V1.0
 */
public class RepairOrderStatusConstant {

    /**
     * 工单状态
     */
    public static class OrderStatus {
        /** 审批中 */
        public static final String PENDING = "1";

        /** 已拒绝,驳回 */
        public static final String REJECTED = "2";

        /** 审批通过，等待维修方处理 */
        public static final String APPROVAL = "3";

        /** 维修方已派出维修负责人 */
        public static final String PROCESSING ="4";

        /** 已完成维修，等待验收 */
        public static final String COMPLETED = "5";

        /** 验收通过，工单关闭 */
        public static final String CLOSED = "6";
    }

    /**
     * 根据工单状态常量获取对应的描述
     * @param status 工单状态常量值
     * @return 状态描述，如果未匹配到则返回"未知状态"
     */
    public static String getOrderStatusDesc(String status) {
        if (status == null) {
            return "未知状态";
        }

        return switch (status) {
            case OrderStatus.PENDING -> "审批中";
            case OrderStatus.REJECTED -> "已拒绝";
            case OrderStatus.APPROVAL -> "审批通过，等待维修方处理";
            case OrderStatus.PROCESSING -> "维修方已派出维修负责人";
            case OrderStatus.COMPLETED -> "已完成维修，等待验收";
            case OrderStatus.CLOSED -> "验收通过，工单关闭";
            default -> "未知状态";
        };
    }


    /**
     * 单个审批步骤状态
     */
    public static class ApprovalStatus {
        /** 待审批 */
        public static final String PENDING = "0";
        /** 已通过 */
        public static final String APPROVED = "1";
        /** 驳回 */
        public static final String REJECTED = "2";
    }

    /**
     * 根据审批步骤状态常量获取对应的描述
     * @param approvalStatus 审批步骤状态常量值
     * @return 状态描述，如果未匹配到则返回"未知审批状态"
     */
    public static String getApprovalStatusDesc(String approvalStatus) {
        if (approvalStatus == null) {
            return "未知审批状态";
        }

        return switch (approvalStatus) {
            case ApprovalStatus.PENDING -> "待审批";
            case ApprovalStatus.APPROVED -> "已通过";
            case ApprovalStatus.REJECTED -> "驳回";
            default -> "未知审批状态";
        };
    }

    /**
     * 审批日志操作类型
     */
    public static class LogAction {
        /** 提交工单 */
        public static final String SUBMIT = "1";
        /** 审批通过 */
        public static final String APPROVE = "2";
        /** 审批驳回 */
        public static final String REJECT = "3";
        /** 指派负责人 */
        public static final String ASSIGNED = "4";
        /** 处理完成 */
        public static final String FINISHED = "5";
        /** 验收通过，关闭工单 */
        public static final String CLOSED = "6";
    }


    /**
     * 根据审批日志操作类型常量获取对应的描述
     * @param action 审批日志操作类型常量值
     * @return 操作描述，如果未匹配到则返回"未知操作"
     */
    public static String getLogActionDesc(String action) {
        if (action == null) {
            return "未知操作";
        }

        return switch (action) {
            case LogAction.SUBMIT -> "提交工单";
            case LogAction.APPROVE -> "审批通过";
            case LogAction.REJECT -> "审批驳回";
            case LogAction.ASSIGNED -> "指派负责人";
            case LogAction.FINISHED -> "处理完成";
            case LogAction.CLOSED -> "验收通过，关闭工单";
            default -> "未知操作";
        };
    }


}
