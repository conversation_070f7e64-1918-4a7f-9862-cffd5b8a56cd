package org.jeecg.modules.demo.emsrepairorders.service;

import org.jeecg.modules.demo.emsrepairorders.entity.EmsRepairOrders;
import org.jeecg.modules.demo.emsrepairorders.dto.EmsRepairOrderCreateDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 维修工单表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
public interface IEmsRepairOrdersService extends IService<EmsRepairOrders> {

    /**
     * 创建维修工单（包含审批步骤和日志）
     * @param createDTO 创建工单DTO
     * @return 工单ID
     */
    String createRepairOrderWithApproval(EmsRepairOrderCreateDTO createDTO);
}
