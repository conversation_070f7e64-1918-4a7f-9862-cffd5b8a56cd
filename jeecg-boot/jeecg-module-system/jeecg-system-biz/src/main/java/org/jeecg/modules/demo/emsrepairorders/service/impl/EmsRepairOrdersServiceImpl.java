package org.jeecg.modules.demo.emsrepairorders.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import org.jeecg.modules.demo.emsrepairorders.entity.EmsRepairOrders;
import org.jeecg.modules.demo.emsrepairorders.mapper.EmsRepairOrdersMapper;
import org.jeecg.modules.demo.emsrepairorders.service.IEmsRepairOrdersService;
import org.jeecg.modules.demo.emsrepairorders.dto.EmsRepairOrderCreateDTO;
import org.jeecg.modules.demo.emsrepairorders.constant.RepairOrderStatusConstant;
import org.jeecg.modules.demo.emsrepairorderapprovalsteps.entity.EmsRepairOrderApprovalSteps;
import org.jeecg.modules.demo.emsrepairorderapprovalsteps.service.IEmsRepairOrderApprovalStepsService;
import org.jeecg.modules.demo.emsrepairorderauditlogs.entity.EmsRepairOrderAuditLogs;
import org.jeecg.modules.demo.emsrepairorderauditlogs.service.IEmsRepairOrderAuditLogsService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.util.oConvertUtils;

import java.util.Date;
import java.util.List;

/**
 * @Description: 维修工单表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class EmsRepairOrdersServiceImpl extends ServiceImpl<EmsRepairOrdersMapper, EmsRepairOrders> implements IEmsRepairOrdersService {

    @Autowired
    private IEmsRepairOrderApprovalStepsService approvalStepsService;

    @Autowired
    private IEmsRepairOrderAuditLogsService auditLogsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRepairOrderWithApproval(EmsRepairOrderCreateDTO createDTO) {
        // 验证DTO结构
        if (createDTO.getRepairOrder() == null) {
            throw new IllegalArgumentException("维修工单基本信息不能为空");
        }

        EmsRepairOrders repairOrder = createDTO.getRepairOrder();
        log.info("开始创建维修工单，工单信息: {}", repairOrder.getFaultTitle());

        try {
            // 1. 设置工单状态并保存维修工单基本信息
            repairOrder.setCurrentStatus(RepairOrderStatusConstant.OrderStatus.PENDING);

            // 保存工单
            this.save(repairOrder);
            String orderId = repairOrder.getId();
            log.info("维修工单保存成功，工单ID: {}", orderId);

            // 2. 创建审批步骤实例
            if (createDTO.getApprovalUserConfig() != null && !createDTO.getApprovalUserConfig().isEmpty()) {
                createApprovalSteps(orderId, createDTO.getApprovalUserConfig());
            }

            // 3. 创建提交工单的审批日志
            createSubmitAuditLog(orderId, repairOrder.getReportId());

            log.info("维修工单创建完成，工单ID: {}", orderId);
            return orderId;

        } catch (Exception e) {
            log.error("创建维修工单失败", e);
            throw new RuntimeException("创建维修工单失败: " + e.getMessage());
        }
    }

    /**
     * 创建审批步骤实例
     */
    private void createApprovalSteps(String orderId, List<EmsRepairOrderCreateDTO.ApprovalStepDTO> approvalSteps) {
        log.info("开始创建审批步骤，工单ID: {}, 步骤数量: {}", orderId, approvalSteps.size());

        for (EmsRepairOrderCreateDTO.ApprovalStepDTO stepDTO : approvalSteps) {
            List<String> selectedUsers = stepDTO.getSelectedUsers();
            if(CollectionUtil.isEmpty(selectedUsers)){
                throw new NullPointerException("审批步骤用户id列表为空");
            }

            // 获取选中的用户ID（默认单选）
            String userId = selectedUsers.get(0);
            if (oConvertUtils.isEmpty(userId) && selectedUsers != null && !selectedUsers.isEmpty()) {
                userId = selectedUsers.get(0);
            }

            if (oConvertUtils.isNotEmpty(userId)) {
                EmsRepairOrderApprovalSteps approvalStep = new EmsRepairOrderApprovalSteps();
                approvalStep.setOrderId(orderId);
                approvalStep.setStep(stepDTO.getSortOrder().toString());
                approvalStep.setRoleCode(stepDTO.getRoleCode());
                approvalStep.setUserId(userId);
                approvalStep.setStatus(RepairOrderStatusConstant.ApprovalStatus.PENDING);

                approvalStepsService.save(approvalStep);
                log.info("审批步骤创建成功，步骤: {}, 角色: {}, 用户: {}", stepDTO.getSortOrder(), stepDTO.getRoleCode(), userId);
            } else {
                log.warn("审批步骤 {} 没有选择用户，跳过创建", stepDTO.getSortOrder());
            }
        }
    }

    /**
     * 创建提交工单的审批日志
     */
    private void createSubmitAuditLog(String orderId, String reportId) {
        log.info("创建提交工单审批日志，工单ID: {}, 提交人: {}", orderId, reportId);

        EmsRepairOrderAuditLogs auditLog = new EmsRepairOrderAuditLogs();
        auditLog.setOrderId(orderId);
        auditLog.setOperatorId(reportId);
        auditLog.setOperatorType("0"); // 0=发起人
        auditLog.setActionType(RepairOrderStatusConstant.LogAction.SUBMIT); // 提交工单
        auditLog.setRemark("用户提交维修工单");
        auditLog.setApprovalStep("0"); // 提交阶段为步骤0

        auditLogsService.save(auditLog);
        log.info("提交工单审批日志创建成功");
    }
}
