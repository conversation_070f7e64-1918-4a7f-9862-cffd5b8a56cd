package org.jeecg.modules.demo.emsrepairorders.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecg.modules.demo.emsrepairorders.entity.EmsRepairOrders;

import java.util.List;

/**
 * @Description: 维修工单创建DTO
 * @Author: jeecg-boot
 * @Date: 2025-08-04
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@Schema(description = "维修工单创建DTO")
public class EmsRepairOrderCreateDTO {

    /**
     * 维修工单基本信息
     */
    @Schema(description = "维修工单基本信息")
    private EmsRepairOrders repairOrder;

    /**
     * 审批步骤配置
     */
    @Schema(description = "审批步骤配置")
    private List<ApprovalStepDTO> approvalUserConfig;

    /**
     * 审批步骤DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "审批步骤DTO")
    public static class ApprovalStepDTO {

        /**
         * 角色ID
         */
        @Schema(description = "角色ID")
        private String roleId;

        /**
         * 角色编码
         */
        @Schema(description = "角色编码")
        private String roleCode;

        /**
         * 角色名称
         */
        @Schema(description = "角色名称")
        private String roleName;

        /**
         * 排序号
         */
        @Schema(description = "排序号")
        private Integer sortOrder;

        /**
         * 选中的用户ID列表（兼容多选）
         */
        @Schema(description = "选中的用户ID列表")
        private List<String> selectedUsers;

    }
}
