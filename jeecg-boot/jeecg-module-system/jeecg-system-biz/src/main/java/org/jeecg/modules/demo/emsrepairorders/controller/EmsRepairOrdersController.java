package org.jeecg.modules.demo.emsrepairorders.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.emsrepairorders.entity.EmsRepairOrders;
import org.jeecg.modules.demo.emsrepairorders.service.IEmsRepairOrdersService;
import org.jeecg.modules.demo.emsrepairorders.dto.EmsRepairOrderCreateDTO;
import org.jeecg.modules.demo.emsrepairorders.constant.RepairOrderStatusConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: 维修工单表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Tag(name="维修工单表")
@RestController
@RequestMapping("/emsrepairorders/emsRepairOrders")
@Slf4j
public class EmsRepairOrdersController extends JeecgController<EmsRepairOrders, IEmsRepairOrdersService> {
	@Autowired
	private IEmsRepairOrdersService emsRepairOrdersService;

	/**
	 * 分页列表查询
	 *
	 * @param emsRepairOrders
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "维修工单表-分页列表查询")
	@Operation(summary="维修工单表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<EmsRepairOrders>> queryPageList(EmsRepairOrders emsRepairOrders,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {


        QueryWrapper<EmsRepairOrders> queryWrapper = QueryGenerator.initQueryWrapper(emsRepairOrders, req.getParameterMap());
		Page<EmsRepairOrders> page = new Page<EmsRepairOrders>(pageNo, pageSize);
		IPage<EmsRepairOrders> pageList = emsRepairOrdersService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加（带审批流程）
	 *
	 * @param createDTO
	 * @return
	 */
	@AutoLog(value = "维修工单表-添加")
	@Operation(summary="维修工单表-添加")
	@RequiresPermissions("emsrepairorders:ems_repair_orders:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody EmsRepairOrderCreateDTO createDTO) {
		try {
			log.info("接收到创建工单请求，DTO: {}", createDTO);
			log.info("工单基本信息: {}", createDTO.getRepairOrder());
			log.info("审批配置: {}", createDTO.getApprovalUserConfig());

			// 调用服务层创建工单（包含审批步骤和日志）
			String orderId = emsRepairOrdersService.createRepairOrderWithApproval(createDTO);

			return Result.OK("工单创建成功！工单ID：" + orderId);
		} catch (Exception e) {
			log.error("创建维修工单失败", e);
			return Result.error("创建工单失败：" + e.getMessage());
		}
	}

	/**
	 *  编辑
	 *
	 * @param emsRepairOrders
	 * @return
	 */
	@AutoLog(value = "维修工单表-编辑")
	@Operation(summary="维修工单表-编辑")
	@RequiresPermissions("emsrepairorders:ems_repair_orders:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody EmsRepairOrders emsRepairOrders) {
		emsRepairOrdersService.updateById(emsRepairOrders);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "维修工单表-通过id删除")
	@Operation(summary="维修工单表-通过id删除")
	@RequiresPermissions("emsrepairorders:ems_repair_orders:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		emsRepairOrdersService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "维修工单表-批量删除")
	@Operation(summary="维修工单表-批量删除")
	@RequiresPermissions("emsrepairorders:ems_repair_orders:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.emsRepairOrdersService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "维修工单表-通过id查询")
	@Operation(summary="维修工单表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<EmsRepairOrders> queryById(@RequestParam(name="id",required=true) String id) {
		EmsRepairOrders emsRepairOrders = emsRepairOrdersService.getById(id);
		if(emsRepairOrders==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(emsRepairOrders);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param emsRepairOrders
    */
    @RequiresPermissions("emsrepairorders:ems_repair_orders:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EmsRepairOrders emsRepairOrders) {
        return super.exportXls(request, emsRepairOrders, EmsRepairOrders.class, "维修工单表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("emsrepairorders:ems_repair_orders:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EmsRepairOrders.class);
    }

}
